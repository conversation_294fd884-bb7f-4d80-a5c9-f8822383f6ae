# Design System Documentation

This document outlines the centralized design system for the Umugore Uzashimwa website. All styling variables are defined in `src/app/globals.css` and used throughout the application via Tailwind CSS classes.

## 🎨 Color Palette

### Primary Colors (Deep Purple Theme)
- **Primary**: `#7E57C2` - Main brand color
- **Primary Light**: `#B085F5` - Lighter variant for backgrounds
- **Primary Dark**: `#4D2C91` - Darker variant for hover states

### Secondary Colors (Light Green Theme)
- **Secondary**: `#A5D6A7` - Complementary color
- **Secondary Light**: `#D7EBD8` - Light backgrounds
- **Secondary Dark**: `#75A478` - Hover states

### Accent Colors (Amber Theme)
- **Accent**: `#FFB74D` - Call-to-action elements
- **Accent Light**: `#FFDB9E` - Light accents
- **Accent Dark**: `#CC8000` - Hover states

### Neutral Colors (Grayscale)
- **Neutral 50-900**: Complete grayscale from lightest to darkest
- Used for text, borders, and backgrounds

### Semantic Colors
- **Success**: Green variants for positive actions
- **Warning**: Orange variants for cautions
- **Error**: Red variants for errors
- **Info**: Blue variants for information

## 📝 Typography

### Font Families
- **Primary**: Open Sans - Body text and general content
- **Heading**: Montserrat - Headings and emphasis

### Font Sizes
- **xs**: 12px - Small labels
- **sm**: 14px - Secondary text
- **base**: 16px - Body text
- **lg**: 18px - Large body text
- **xl**: 20px - Small headings
- **2xl**: 24px - Medium headings
- **3xl**: 30px - Large headings
- **4xl**: 36px - Extra large headings
- **5xl**: 48px - Hero headings
- **6xl**: 60px - Display headings

### Font Weights
- **Light**: 300
- **Normal**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700
- **Extrabold**: 800

## 🏗️ Layout & Spacing

### Spacing Scale
- **xs**: 4px - Minimal spacing
- **sm**: 8px - Small spacing
- **md**: 16px - Medium spacing
- **lg**: 24px - Large spacing
- **xl**: 32px - Extra large spacing
- **2xl**: 48px - Section spacing
- **3xl**: 64px - Large section spacing
- **4xl**: 96px - Hero spacing

### Border Radius
- **sm**: 4px - Small elements
- **md**: 6px - Default radius
- **lg**: 8px - Cards and containers
- **xl**: 12px - Large containers
- **2xl**: 16px - Extra large containers
- **full**: 9999px - Circular elements

## 🎯 Usage Guidelines

### Changing Colors
To change the color scheme, update the CSS variables in `src/app/globals.css`:

```css
:root {
  --color-primary: #YOUR_NEW_COLOR;
  --color-primary-light: #YOUR_LIGHT_VARIANT;
  --color-primary-dark: #YOUR_DARK_VARIANT;
}
```

### Using Colors in Components
Always use Tailwind classes that reference the CSS variables:

```jsx
// ✅ Good - Uses design system
<button className="bg-primary text-white hover:bg-primary-dark">
  Click me
</button>

// ❌ Bad - Hardcoded colors
<button className="bg-[#7E57C2] text-white hover:bg-[#4D2C91]">
  Click me
</button>
```

### Component Classes
Use the predefined component classes for consistency:

```jsx
// Buttons
<button className="btn btn-primary">Primary Button</button>
<button className="btn btn-outline">Outline Button</button>
<button className="btn btn-ghost">Ghost Button</button>

// Cards
<div className="card">Card content</div>

// Forms
<input className="form-input" />
<label className="form-label">Label</label>
```

## 🔧 Customization

### Adding New Colors
1. Add CSS variable in `globals.css`
2. Add Tailwind mapping in `tailwind.config.js`
3. Document the new color here

### Adding New Components
1. Define component styles in `@layer components` in `globals.css`
2. Use existing design tokens for consistency
3. Document usage patterns

## 📱 Responsive Design

The design system includes responsive utilities:
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Use responsive prefixes: `md:text-lg`, `lg:p-8`

## ♿ Accessibility

- Color contrast ratios meet WCAG AA standards
- Focus states are clearly defined
- Semantic color usage (success, warning, error)
- Proper font sizes for readability

## 🚀 Benefits

1. **Consistency**: All components use the same design tokens
2. **Maintainability**: Change one variable to update the entire theme
3. **Performance**: CSS variables are efficient and cacheable
4. **Developer Experience**: Clear naming conventions and documentation
5. **Flexibility**: Easy to create themes or dark mode variants

## 📋 Checklist for New Components

- [ ] Uses design system colors (no hardcoded values)
- [ ] Uses design system spacing and typography
- [ ] Follows naming conventions
- [ ] Includes hover and focus states
- [ ] Is responsive
- [ ] Meets accessibility standards
- [ ] Is documented

---

**Note**: This design system is the single source of truth for all styling. Always refer to this documentation when creating or modifying components.
