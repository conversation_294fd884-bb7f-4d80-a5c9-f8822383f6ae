#!/usr/bin/env node

/**
 * <PERSON>ript to help clear Vercel caches
 * Run this after deployment to ensure fresh content
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Update service worker cache version
function updateServiceWorkerVersion() {
  const swPath = path.join(__dirname, '../public/sw.js');
  let swContent = fs.readFileSync(swPath, 'utf8');
  
  // Update the cache version with current timestamp
  const newVersion = Date.now();
  swContent = swContent.replace(
    /const CACHE_VERSION = \d+;/,
    `const CACHE_VERSION = ${newVersion};`
  );
  
  fs.writeFileSync(swPath, swContent);
  console.log(`✅ Updated service worker cache version to: ${newVersion}`);
}

// Create a cache-busting manifest
function createCacheBustManifest() {
  const manifest = {
    timestamp: Date.now(),
    version: process.env.VERCEL_GIT_COMMIT_SHA || 'local',
    message: 'Cache busting manifest - update this to force cache refresh'
  };
  
  const manifestPath = path.join(__dirname, '../public/cache-bust.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log(`✅ Created cache-bust manifest: ${manifest.timestamp}`);
}

// Instructions for manual cache clearing
function showInstructions() {
  console.log(`
🧹 CACHE CLEARING GUIDE

1. DEVELOPMENT (Local):
   • Open browser console and run: clearCache()
   • Or use the cache control panel (red gear icon)
   • Or hard refresh: Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)

2. VERCEL DEPLOYMENT:
   • Go to Vercel Dashboard → Your Project → Functions tab
   • Click "Purge Cache" button
   • Or use Vercel CLI: vercel --prod --force

3. BROWSER CACHE (Users):
   • Service worker will auto-update on next visit
   • Users can hard refresh to force update
   • Cache expires automatically after TTL

4. API CACHE:
   • Backend cache: Contact backend admin
   • CDN cache: Usually auto-expires in 5-10 minutes

📝 TROUBLESHOOTING:
   • If changes don't appear: Clear all caches + hard refresh
   • If API data is stale: Check backend cache settings
   • If images don't update: Check CDN cache headers

🔧 AUTOMATED SOLUTIONS:
   • This script updates SW version automatically
   • Cache-bust manifest helps track deployments
   • Development mode has shorter cache TTL
  `);
}

// Main execution
function main() {
  console.log('🚀 Starting cache management...\n');
  
  try {
    updateServiceWorkerVersion();
    createCacheBustManifest();
    showInstructions();
    
    console.log('\n✅ Cache management completed successfully!');
  } catch (error) {
    console.error('❌ Error during cache management:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  updateServiceWorkerVersion,
  createCacheBustManifest,
  showInstructions
};
