#!/usr/bin/env node

/**
 * Force cache refresh script for API URL change
 * This script helps clear all caches when the API URL has been updated
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 FORCING CACHE REFRESH FOR NEW API URL');
console.log('=====================================');

// 1. Update service worker cache version
function updateServiceWorkerVersion() {
  const swPath = path.join(__dirname, '../public/sw.js');
  let swContent = fs.readFileSync(swPath, 'utf8');
  
  const newVersion = Date.now();
  swContent = swContent.replace(
    /const CACHE_VERSION = \d+;/,
    `const CACHE_VERSION = ${newVersion};`
  );
  
  fs.writeFileSync(swPath, swContent);
  console.log(`✅ Updated service worker cache version to: ${newVersion}`);
}

// 2. Update API cache buster version
function updateApiCacheBuster() {
  const apiPath = path.join(__dirname, '../src/lib/api.js');
  let apiContent = fs.readFileSync(apiPath, 'utf8');
  
  const newVersion = Date.now();
  apiContent = apiContent.replace(
    /const API_URL_CHANGE_VERSION = '[^']+';/,
    `const API_URL_CHANGE_VERSION = '${newVersion}';`
  );
  
  fs.writeFileSync(apiPath, apiContent);
  console.log(`✅ Updated API cache buster version to: ${newVersion}`);
}

// 3. Update cache-bust manifest
function updateCacheBustManifest() {
  const manifest = {
    timestamp: Date.now(),
    version: process.env.VERCEL_GIT_COMMIT_SHA || 'api-url-update',
    message: 'FORCE REFRESH FOR NEW API URL: https://api.umugoreuzashimwa.org',
    apiUrl: 'https://api.umugoreuzashimwa.org/api'
  };
  
  const manifestPath = path.join(__dirname, '../public/cache-bust.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log(`✅ Updated cache-bust manifest: ${manifest.timestamp}`);
}

// 4. Clear Next.js build cache
function clearNextCache() {
  const nextCachePath = path.join(__dirname, '../.next');
  if (fs.existsSync(nextCachePath)) {
    fs.rmSync(nextCachePath, { recursive: true, force: true });
    console.log('✅ Cleared Next.js build cache');
  }
}

// Run all cache refresh steps
try {
  updateServiceWorkerVersion();
  updateApiCacheBuster();
  updateCacheBustManifest();
  clearNextCache();
  
  console.log('\n🎉 CACHE REFRESH COMPLETE!');
  console.log('\nNext steps:');
  console.log('1. Run: npm run build');
  console.log('2. Deploy to Vercel');
  console.log('3. In Vercel dashboard, go to Functions tab and click "Purge Cache"');
  console.log('4. Users may need to hard refresh (Ctrl+Shift+R) to see changes');
  
} catch (error) {
  console.error('❌ Error during cache refresh:', error.message);
  process.exit(1);
}
