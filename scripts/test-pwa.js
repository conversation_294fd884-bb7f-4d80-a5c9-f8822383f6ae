#!/usr/bin/env node

/**
 * PWA Testing and Validation Script
 * Tests PWA functionality, manifest validation, and service worker registration
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 PWA Testing and Validation');
console.log('==============================\n');

// Test 1: Validate manifest.json
function validateManifest() {
  console.log('📱 Testing Web App Manifest...');
  
  try {
    const manifestPath = path.join(__dirname, '../public/manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    const requiredFields = ['name', 'short_name', 'start_url', 'display', 'icons'];
    const missingFields = requiredFields.filter(field => !manifest[field]);
    
    if (missingFields.length > 0) {
      console.log('❌ Missing required fields:', missingFields.join(', '));
      return false;
    }
    
    // Validate icons
    if (!manifest.icons || manifest.icons.length === 0) {
      console.log('❌ No icons defined in manifest');
      return false;
    }
    
    // Check if icon files exist
    for (const icon of manifest.icons) {
      const iconPath = path.join(__dirname, '../public', icon.src);
      if (!fs.existsSync(iconPath)) {
        console.log(`❌ Icon file not found: ${icon.src}`);
        return false;
      }
    }
    
    console.log('✅ Manifest validation passed');
    console.log(`   - Name: ${manifest.name}`);
    console.log(`   - Short Name: ${manifest.short_name}`);
    console.log(`   - Icons: ${manifest.icons.length} defined`);
    console.log(`   - Display Mode: ${manifest.display}`);
    console.log(`   - Theme Color: ${manifest.theme_color}`);
    
    return true;
  } catch (error) {
    console.log('❌ Manifest validation failed:', error.message);
    return false;
  }
}

// Test 2: Validate Service Worker
function validateServiceWorker() {
  console.log('\n⚙️ Testing Service Worker...');
  
  try {
    const swPath = path.join(__dirname, '../public/sw.js');
    const swContent = fs.readFileSync(swPath, 'utf8');
    
    // Check for required service worker events
    const requiredEvents = ['install', 'activate', 'fetch'];
    const missingEvents = requiredEvents.filter(event => 
      !swContent.includes(`addEventListener('${event}'`)
    );
    
    if (missingEvents.length > 0) {
      console.log('❌ Missing service worker events:', missingEvents.join(', '));
      return false;
    }
    
    // Check for cache management
    if (!swContent.includes('caches.open')) {
      console.log('❌ No cache management found in service worker');
      return false;
    }
    
    // Check for update handling
    if (!swContent.includes('skipWaiting') || !swContent.includes('clients.claim')) {
      console.log('⚠️ Update handling may not be optimal');
    }
    
    console.log('✅ Service Worker validation passed');
    console.log('   - Install event: ✓');
    console.log('   - Activate event: ✓');
    console.log('   - Fetch event: ✓');
    console.log('   - Cache management: ✓');
    console.log('   - Update handling: ✓');
    
    return true;
  } catch (error) {
    console.log('❌ Service Worker validation failed:', error.message);
    return false;
  }
}

// Test 3: Validate Icons and Assets
function validateAssets() {
  console.log('\n🖼️ Testing Icons and Assets...');
  
  const requiredAssets = [
    'public/favicon.ico',
    'public/icons/no-bg-logo.png',
    'public/manifest.json',
    'public/sw.js',
    'public/offline.html'
  ];
  
  let allAssetsExist = true;
  
  for (const asset of requiredAssets) {
    const assetPath = path.join(__dirname, '..', asset);
    if (fs.existsSync(assetPath)) {
      console.log(`✅ ${asset}`);
    } else {
      console.log(`❌ ${asset} - Missing`);
      allAssetsExist = false;
    }
  }
  
  return allAssetsExist;
}

// Test 4: Validate HTML Meta Tags
function validateMetaTags() {
  console.log('\n🏷️ Testing HTML Meta Tags...');
  
  try {
    const layoutPath = path.join(__dirname, '../src/app/layout.js');
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    const requiredMetaTags = [
      'theme-color',
      'apple-mobile-web-app-capable',
      'apple-mobile-web-app-status-bar-style',
      'apple-mobile-web-app-title'
    ];
    
    let allMetaTagsPresent = true;
    
    for (const metaTag of requiredMetaTags) {
      if (layoutContent.includes(metaTag)) {
        console.log(`✅ ${metaTag}`);
      } else {
        console.log(`❌ ${metaTag} - Missing`);
        allMetaTagsPresent = false;
      }
    }
    
    // Check for manifest link
    if (layoutContent.includes('rel="manifest"')) {
      console.log('✅ Manifest link');
    } else {
      console.log('❌ Manifest link - Missing');
      allMetaTagsPresent = false;
    }
    
    return allMetaTagsPresent;
  } catch (error) {
    console.log('❌ Meta tags validation failed:', error.message);
    return false;
  }
}

// Test 5: Check PWA Components
function validatePWAComponents() {
  console.log('\n🧩 Testing PWA Components...');
  
  const requiredComponents = [
    'src/components/pwa/PWAProvider.js',
    'src/components/pwa/InstallPrompt.js',
    'src/components/pwa/UpdatePrompt.js',
    'src/lib/serviceWorker.js'
  ];
  
  let allComponentsExist = true;
  
  for (const component of requiredComponents) {
    const componentPath = path.join(__dirname, '..', component);
    if (fs.existsSync(componentPath)) {
      console.log(`✅ ${component}`);
    } else {
      console.log(`❌ ${component} - Missing`);
      allComponentsExist = false;
    }
  }
  
  return allComponentsExist;
}

// Test 6: Generate PWA Report
function generateReport(results) {
  console.log('\n📊 PWA Validation Report');
  console.log('========================');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(result => result).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  console.log(`\nOverall Score: ${score}% (${passedTests}/${totalTests} tests passed)\n`);
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}`);
  });
  
  if (score === 100) {
    console.log('\n🎉 Congratulations! Your PWA is ready for production!');
    console.log('\nNext Steps:');
    console.log('1. Test on actual devices (iOS, Android)');
    console.log('2. Run Lighthouse PWA audit');
    console.log('3. Test offline functionality');
    console.log('4. Verify install prompts work');
    console.log('5. Test update mechanism');
  } else {
    console.log('\n⚠️ Some issues need to be addressed before production deployment.');
    console.log('\nRecommendations:');
    console.log('1. Fix all failing tests above');
    console.log('2. Test PWA functionality manually');
    console.log('3. Run additional validation tools');
  }
  
  return score;
}

// Run all tests
async function runTests() {
  const results = {
    'Web App Manifest': validateManifest(),
    'Service Worker': validateServiceWorker(),
    'Icons and Assets': validateAssets(),
    'HTML Meta Tags': validateMetaTags(),
    'PWA Components': validatePWAComponents()
  };
  
  const score = generateReport(results);
  
  // Exit with appropriate code
  process.exit(score === 100 ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
