const fs = require('fs');
const path = require('path');

// Create a simple SVG icon with the ministry initials
function createSVGIcon(size) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A8B9A3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B7A5A;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="url(#grad)"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-weight="bold" 
        font-size="${size * 0.3}" fill="#FEFCF7">UU</text>
  <text x="50%" y="${size * 0.8}" dominant-baseline="middle" text-anchor="middle" 
        font-family="Arial, sans-serif" font-weight="normal" 
        font-size="${size * 0.12}" fill="#FEFCF7">MINISTRY</text>
</svg>`;
}

// Icon sizes needed for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate SVG icons
iconSizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated ${filename}`);
});

// Create a simple PNG placeholder using Canvas (if available) or just copy SVG
iconSizes.forEach(size => {
  const svgPath = path.join(iconsDir, `icon-${size}x${size}.svg`);
  const pngPath = path.join(iconsDir, `icon-${size}x${size}.png`);
  
  // For now, just copy the SVG as PNG (browsers will handle SVG icons)
  // In production, you'd want to convert SVG to PNG using a proper tool
  fs.copyFileSync(svgPath, pngPath);
  console.log(`Generated icon-${size}x${size}.png`);
});

console.log('All icons generated successfully!');
console.log('Note: For production, convert SVG icons to PNG format using a proper tool like sharp or imagemagick.');
