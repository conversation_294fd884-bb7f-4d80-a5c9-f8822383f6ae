import './globals.css';
import { Open_Sans, Montserrat } from 'next/font/google';
import PWAProvider from '../components/pwa/PWAProvider';
import DevCacheControl from '../components/DevCacheControl';
import { Analytics } from "@vercel/analytics/next";

const openSans = Open_Sans({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-opensans',
});

const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
});

export const metadata = {
  title: 'Umugore Uzashimwa Ministry',
  description: 'Umugore wubaha Uwiteka niwe Uzashimwa - A ministry dedicated to encourage women through biblical principles',
  keywords: 'biblical principles, women ministry, faith, christian women, testimonies, Kinyarwanda, Rwanda',
  authors: [{ name: 'Umugore Uzashimwa Ministry' }],
  creator: '<PERSON><PERSON><PERSON>wa Ministry',
  publisher: '<PERSON><PERSON><PERSON>himwa Ministry',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://umugoreuzashimwa.org'),
  alternates: {
    canonical: '/',
  },
  manifest: '/manifest.json',
  openGraph: {
    title: 'Umugore Uzashimwa Ministry',
    description: 'Umugore wubaha Uwiteka niwe Uzashimwa - A ministry dedicated to encourage women through biblical principles',
    url: 'https://umugoreuzashimwa.org',
    siteName: 'Umugore Uzashimwa Ministry',
    images: [
      {
        url: '/icons/no-bg-logo.png',
        width: 1200,
        height: 630,
        alt: 'Umugore Uzashimwa Ministry',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Umugore Uzashimwa Ministry',
    description: 'Umugore wubaha Uwiteka niwe Uzashimwa - A ministry dedicated to encourage women through biblical principles',
    images: ['/icons/no-bg-logo.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="rw" className={`${openSans.variable} ${montserrat.variable}`}>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#A8B9A3" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Umugore Uzashimwa" />
        <link rel="apple-touch-icon" href="/icons/no-bg-logo.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/no-bg-logo.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/no-bg-logo.png" />
        <link rel="shortcut icon" href="/icons/no-bg-logo.png" />
        <link rel="icon" href="/icons/no-bg-logo.png" />
      </head>
      <body className="font-opensans">
        <PWAProvider>
          {children}
          <DevCacheControl />
        </PWAProvider>
        <Analytics />
      </body>
    </html>
  );
}
