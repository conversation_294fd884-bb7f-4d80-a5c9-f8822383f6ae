@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System - Single Source of Truth for Colors, Typography, and Spacing */
:root {
  /* Primary Colors - Warm Earth Tones Theme */
  --color-primary: #6B7A5A;
  --color-primary-light: #A8B9A3;
  --color-primary-dark: #5A6B49;

  /* Alternative Color Schemes - Uncomment to switch themes */

  /* Blue Theme */
  /* --color-primary: #3B82F6;
     --color-primary-light: #93C5FD;
     --color-primary-dark: #2563EB; */

  /* Green Theme */
  /* --color-primary: #10B981;
     --color-primary-light: #6EE7B7;
     --color-primary-dark: #059669; */

  /* Rose Theme */
  /* --color-primary: #F43F5E;
     --color-primary-light: #FB7185;
     --color-primary-dark: #E11D48; */

  /* Secondary Colors - Warm Cream Theme */
  --color-secondary: #F5F1E8;
  --color-secondary-light: #FEFCF7;
  --color-secondary-dark: #E8E0D3;

  /* Accent Colors - Soft Terracotta Theme */
  --color-accent: #D4A574;
  --color-accent-light: #E4C4A0;
  --color-accent-dark: #B8935F;

  /* Neutral Colors - Grayscale */
  --color-neutral-50: #FAFAFA;
  --color-neutral-100: #F5F5F5;
  --color-neutral-200: #E5E5E5;
  --color-neutral-300: #D4D4D4;
  --color-neutral-400: #A3A3A3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  /* Semantic Colors */
  --color-success: #22C55E;
  --color-success-light: #86EFAC;
  --color-success-dark: #16A34A;

  --color-warning: #F59E0B;
  --color-warning-light: #FCD34D;
  --color-warning-dark: #D97706;

  --color-error: #EF4444;
  --color-error-light: #FCA5A5;
  --color-error-dark: #DC2626;

  --color-info: #3B82F6;
  --color-info-light: #93C5FD;
  --color-info-dark: #2563EB;

  /* Social Media Brand Colors */
  --color-facebook: #1877F2;
  --color-facebook-hover: #166FE5;
  --color-twitter: #1DA1F2;
  --color-twitter-hover: #1A91DA;
  --color-linkedin: #0A66C2;
  --color-linkedin-hover: #095BA8;
  --color-whatsapp: #25D366;
  --color-whatsapp-hover: #22C55E;
  --color-telegram: #0088CC;
  --color-telegram-hover: #007BB8;
  --color-instagram: #E4405F;
  --color-instagram-hover: #C13584;

  /* Background Colors */
  --color-background: #FEFCF7;
  --color-background-secondary: #F5F1E8;
  --color-background-tertiary: #E8E0D3;

  /* Text Colors */
  --color-text-primary: #3A3A3A;
  --color-text-secondary: #6B7A5A;
  --color-text-tertiary: var(--color-neutral-500);
  --color-text-inverse: #FEFCF7;

  /* Border Colors */
  --color-border: var(--color-neutral-200);
  --color-border-light: var(--color-neutral-100);
  --color-border-dark: var(--color-neutral-300);

  /* Typography Scale */
  --font-family-primary: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-heading: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

@layer base {
  /* Base Typography */
  body {
    font-family: var(--font-family-primary);
    color: var(--color-text-primary);
    background-color: var(--color-background);
    line-height: var(--line-height-normal);
  }

  /* Heading Styles */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
  }

  h1 {
    font-size: var(--font-size-4xl);
  }

  @media (min-width: 768px) {
    h1 {
      font-size: var(--font-size-5xl);
    }
  }

  h2 {
    font-size: var(--font-size-3xl);
  }

  h3 {
    font-size: var(--font-size-2xl);
  }

  h4 {
    font-size: var(--font-size-xl);
  }

  h5 {
    font-size: var(--font-size-lg);
  }

  h6 {
    font-size: var(--font-size-base);
  }

  /* Link Styles */
  a {
    color: var(--color-primary);
    transition: color var(--transition-fast);
  }

  a:hover {
    color: var(--color-primary-dark);
  }

  /* Form Elements */
  input, textarea, select {
    font-family: var(--font-family-primary);
  }
}

@layer components {
  /* Layout Components */
  .container-custom {
    max-width: 80rem; /* 1280px */
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  @media (min-width: 640px) {
    .container-custom {
      padding-left: var(--spacing-lg);
      padding-right: var(--spacing-lg);
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: var(--spacing-xl);
      padding-right: var(--spacing-xl);
    }
  }

  .section {
    padding-top: var(--spacing-3xl);
    padding-bottom: var(--spacing-3xl);
  }

  @media (min-width: 768px) {
    .section {
      padding-top: var(--spacing-4xl);
      padding-bottom: var(--spacing-4xl);
    }
  }

  /* Button Components */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    transition: all var(--transition-fast);
    cursor: pointer;
    border: 1px solid transparent;
    text-decoration: none;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
    border-color: var(--color-primary);
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
    color: var(--color-text-inverse);
  }

  .btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
    border-color: var(--color-secondary);
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--color-secondary-dark);
    border-color: var(--color-secondary-dark);
    color: var(--color-text-inverse);
  }

  .btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
  }

  .btn-outline:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
  }

  .btn-ghost {
    background-color: transparent;
    color: var(--color-primary);
    border-color: transparent;
  }

  .btn-ghost:hover:not(:disabled) {
    background-color: var(--color-background-secondary);
    color: var(--color-primary);
  }

  /* Size Variants */
  .btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
  }

  /* Card Components */
  .card {
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: border-color var(--transition-fast);
  }

  .card:hover {
    border-color: var(--color-border-dark);
  }

  .card-shadow {
    box-shadow: var(--shadow-md);
  }

  .card-shadow:hover {
    box-shadow: var(--shadow-lg);
  }

  /* Form Components */
  .form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  }

  .form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgb(126 87 194 / 0.1);
  }

  .form-label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xs);
  }

  /* Utility Classes */
  .text-primary {
    color: var(--color-primary);
  }

  .text-secondary {
    color: var(--color-text-secondary);
  }

  .text-tertiary {
    color: var(--color-text-tertiary);
  }

  .bg-primary {
    background-color: var(--color-primary);
  }

  .bg-secondary {
    background-color: var(--color-secondary);
  }

  .bg-accent {
    background-color: var(--color-accent);
  }

  .border-primary {
    border-color: var(--color-primary);
  }

  .border-secondary {
    border-color: var(--color-secondary);
  }
}
