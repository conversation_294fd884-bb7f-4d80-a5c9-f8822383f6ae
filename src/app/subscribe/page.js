'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import { FaCheck, FaCheckCircle, FaExclamationTriangle, FaUserPlus } from 'react-icons/fa';
import { api } from '../../lib/api';

export default function SubscribePage() {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    interests: []
  });
  const [categories, setCategories] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const [loading, setLoading] = useState(true);

  // Fetch categories for interests
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.categories.getAll({ pageSize: 20 });
        setCategories(response.results || []);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleInterestChange = (categoryId) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(categoryId)
        ? prev.interests.filter(id => id !== categoryId)
        : [...prev.interests, categoryId]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage('');

    try {
      // Validate required fields
      if (!formData.first_name.trim() || !formData.last_name.trim() || !formData.email.trim()) {
        throw new Error('Uzuza amakuru yose akenewe');
      }

      // Prepare submission data
      const submissionData = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        email: formData.email.trim(),
        interests: formData.interests
      };

      // Submit to API
      await api.subscriptions.create(submissionData);

      // Success
      setIsSuccess(true);
      setMessage('Murakoze! Mwiyandikishije neza. Muzahabwa amakuru agezweho kuri email yanyu.');

      // Reset form
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        interests: []
      });

      // Scroll to top of form to show success message
      setTimeout(() => {
        const formElement = document.querySelector('form');
        if (formElement) {
          formElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);

    } catch (error) {
      setIsSuccess(false);
      setMessage(error.message || 'Habaye ikosa. Ongera ugerageze nyuma.');
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="bg-primary py-12 text-white">
        <div className="container-custom">
          <h1 className="mb-4">Iyandikishe ku butumwa bwaburi munsi</h1>
          <p className="text-lg max-w-3xl">
            Iyandikishe ujye uhabwa amakuru ya ministry agezweho
          </p>
        </div>
      </section>

      {/* Subscription Form */}
      <section className="section bg-neutral-100">
        <div className="container-custom max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Form */}
            <div>
              <h2 className="mb-6">Iyandikishe nonaha</h2>
              <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg border border-neutral-200">
                <div className="mb-6">
                  <label htmlFor="first_name" className="block text-neutral-700 font-medium mb-2">
                    Izina ryawe ryambere <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Injiza izina ryawe ryambere"
                    required
                    disabled={isSubmitting}
                  />
                </div>
                <div className="mb-6">
                  <label htmlFor="last_name" className="block text-neutral-700 font-medium mb-2">
                    Izina ryawe rya nyuma <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Injiza izina ryawe rya nyuma"
                    required
                    disabled={isSubmitting}
                  />
                </div>
                <div className="mb-6">
                  <label htmlFor="email" className="block text-neutral-700 font-medium mb-2">
                    Aderesi ya imeri <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Injiza aderesi yawe ya imeri"
                    required
                    disabled={isSubmitting}
                  />
                </div>
                <div className="mb-6">
                  <label className="block text-neutral-700 font-medium mb-2">Ibyo ukunda (Hitamo)</label>
                  <div className="space-y-2">
                    {loading ? (
                      // Loading skeleton for categories
                      <>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-neutral-300 rounded mr-2 animate-pulse"></div>
                          <div className="h-4 bg-neutral-300 rounded w-24 animate-pulse"></div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-neutral-300 rounded mr-2 animate-pulse"></div>
                          <div className="h-4 bg-neutral-300 rounded w-32 animate-pulse"></div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-neutral-300 rounded mr-2 animate-pulse"></div>
                          <div className="h-4 bg-neutral-300 rounded w-28 animate-pulse"></div>
                        </div>
                      </>
                    ) : categories.length > 0 ? (
                      categories.map((category) => (
                        <div key={category.id} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`category-${category.id}`}
                            checked={formData.interests.includes(category.id)}
                            onChange={() => handleInterestChange(category.id)}
                            className="mr-2"
                            disabled={isSubmitting}
                          />
                          <label htmlFor={`category-${category.id}`}>{category.name}</label>
                        </div>
                      ))
                    ) : (
                      <p className="text-neutral-500 text-sm">Nta byiciro biraboneka ubu</p>
                    )}
                  </div>
                </div>
                <div className="mb-6">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="privacy"
                      className="mr-2"
                      required
                      disabled={isSubmitting}
                    />
                    <label htmlFor="privacy" className="text-sm">
                      Nemera ubwirinzi bw'amakuru ndetse nemeye ko mwajya mumpa amakuru kuri email <span className="text-red-500">*</span>
                    </label>
                  </div>
                </div>

                {message && (
                  <div className={`mb-6 p-6 rounded-lg transition-all duration-500 ease-in-out transform ${isSuccess ? 'bg-green-50 text-green-800 border-2 border-green-200 animate-pulse' : 'bg-red-50 text-red-800 border-2 border-red-200'}`}>
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {isSuccess ? (
                          <FaCheckCircle className="text-green-600 text-xl animate-bounce" />
                        ) : (
                          <FaExclamationTriangle className="text-red-600 text-xl" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className={`font-semibold mb-1 ${isSuccess ? 'text-green-800' : 'text-red-800'}`}>
                          {isSuccess ? 'Mwiyandikishije neza!' : 'Habaye ikosa'}
                        </h4>
                        <p className={`text-sm ${isSuccess ? 'text-green-700' : 'text-red-700'}`}>
                          {message}
                        </p>
                        {isSuccess && (
                          <div className="mt-3 text-xs text-green-600 space-y-1">
                            <p className="flex items-center gap-2">
                              <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                              Muzahabwa amakuru agezweho kuri email yanyu
                            </p>
                            <p className="flex items-center gap-2">
                              <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                              Mushobora guhindura amahitamo yanyu igihe cyose
                            </p>
                            <p className="flex items-center gap-2">
                              <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                              Murakaza neza mu muryango wacu!
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  className="btn btn-primary w-full flex items-center justify-center gap-2"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Iyandikisha...
                    </>
                  ) : (
                    <>
                      <FaUserPlus />
                      Iyandikishe ubu
                    </>
                  )}
                </button>
              </form>
            </div>

            {/* Benefits */}
            <div>
              <h2 className="mb-6">Inyungu zo guhabwa amakuru</h2>
              <div className="bg-white p-8 rounded-lg border border-neutral-200 mb-8">
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Guhora ugezwaho amakuru mashya</h3>
                      <p className="text-neutral-600">
                        Guhora ugezwaho amakuru mashya, kwibutswa amagahunda cyangwa ibikorwa.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Gukomeza kwihugura no kumenya</h3>
                      <p className="text-neutral-600">
                        Gukomeza kwihugura no kumenya ibintu bishya bijyanye n'ubugore n'ukwizera.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Gukomeza umubano na ministry</h3>
                      <p className="text-neutral-600">
                        Gukomeza umubano na ministry, kubika neza ubutumwa wifuza guhorana.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-primary-light p-2 rounded-full text-primary mr-3 mt-1">
                      <FaCheck />
                    </div>
                    <div>
                      <h3 className="font-medium">Kubana n'abandi bagore</h3>
                      <p className="text-neutral-600">
                        Kubana n'abandi bagore bafite intego imwe yo gukura mu kwizera no mu buzima.
                      </p>
                    </div>
                  </li>
                </ul>
              </div>

              {/* Testimonial */}
              {/* <div className="bg-secondary-light p-8 rounded-lg">
                <div className="text-accent text-4xl font-serif mb-3">"</div>
                <p className="text-lg italic mb-4">
                  Amakuru ya ministry yatumye nkura cyane mu kwizera kwanjye. Inyandiko zifasha cyane mu buzima bwanjye bwa buri munsi kandi zituma nkura mu kwizera.
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-neutral-300 mr-3"></div>
                  <div>
                    <div className="font-medium">Umugore w'umwizera</div>
                    <div className="text-sm text-neutral-700">Wiyandikishije kuva 2023</div>
                  </div>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
