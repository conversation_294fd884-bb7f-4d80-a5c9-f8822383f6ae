'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import TestimonialCard from '../../components/TestimonialCard';
import NewsletterForm from '../../components/NewsletterForm';
import TestimonyForm from '../../components/TestimonyForm';
import Pagination from '../../components/Pagination';
import ErrorMessage from '../../components/ErrorMessage';
import { TestimonialGridSkeleton } from '../../components/SkeletonCard';
import { api } from '../../lib/api';
import { ORDERING_OPTIONS } from '../../lib/constants';

export default function TestimoniesPage() {
  // State management
  const [testimonials, setTestimonials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch testimonials
  const fetchTestimonials = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        page: currentPage,
        pageSize: 6,
        ordering: ORDERING_OPTIONS.NEWEST_FIRST,
      };

      const response = await api.testimonies.getAll(params);
      setTestimonials(response.results || []);
      setTotalPages(response.total_pages || 1);
    } catch (err) {
      setError(err.message);
      setTestimonials([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, [currentPage]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="py-12 text-white bg-primary-light">
        <div className="container-custom">
          <h1 className="mb-4">Ubuhamya bushimishije</h1>
          <p className="text-lg max-w-3xl">
            Soma ubuhamya bw'abagore babaye ibyaremwe bishya. Wumve kwizera kwabo, ukomezwe nako.
          </p>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="section bg-background">
        <div className="container-custom">
          {loading ? (
            <TestimonialGridSkeleton count={6} />
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchTestimonials}
              className="max-w-md mx-auto"
            />
          ) : testimonials.length > 0 ? (
            <>
              <div className="grid md:grid-cols-2 gap-8">
                {testimonials.map((testimonial) => (
                  <TestimonialCard key={testimonial.id} testimonial={testimonial} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  className="mt-12"
                />
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-primary mb-2">Nta buhamya buraboneka ubu</h3>
              <p className="text-primary-dark">Uze kugenzura hanyuma ko hagize inkuru zishimishije zo mu muryango wacu.</p>
            </div>
          )}
        </div>
      </section>

      {/* Share Your Story */}
      <section className="section bg-secondary-light">
        <div className="container-custom max-w-3xl mx-auto text-center">
          <h2 className="mb-6">Sangiza inkuru yawe</h2>
          <p className="text-lg mb-8">
            Ese nawe haricyo wasangiza abandi? Twakwishimira kumva ubuhamya bwawe, tukazabusangiza n'abandi.
          </p>
          <TestimonyForm />
        </div>
      </section>

      {/* Newsletter */}
      <section className="section text-white bg-primary-light">
        <div className="container-custom max-w-4xl mx-auto text-center">
          <h2 className="mb-4">Bana natwe muri minisiteri</h2>
          <p className="mb-8">Iyandikishe maze ujye ubasha guhabwa inyandiko nshya, umenyeshwe gahunda za minisiteri mugihe zihari.</p>
          <NewsletterForm />
        </div>
      </section>

      <Footer />
    </div>
  );
}
