// Social sharing utilities

/**
 * Generate social media sharing URLs
 */
export const socialShareUrls = {
  facebook: (url, title) => {
    const params = new URLSearchParams({
      u: url,
      quote: title,
    });
    return `https://www.facebook.com/sharer/sharer.php?${params.toString()}`;
  },

  twitter: (url, title, hashtags = []) => {
    const params = new URLSearchParams({
      url: url,
      text: title,
      hashtags: hashtags.join(','),
    });
    return `https://twitter.com/intent/tweet?${params.toString()}`;
  },

  linkedin: (url, title, summary = '') => {
    const params = new URLSearchParams({
      url: url,
      title: title,
      summary: summary,
    });
    return `https://www.linkedin.com/sharing/share-offsite/?${params.toString()}`;
  },

  whatsapp: (url, title) => {
    const text = `${title} ${url}`;
    const params = new URLSearchParams({
      text: text,
    });
    return `https://wa.me/?${params.toString()}`;
  },

  telegram: (url, title) => {
    const params = new URLSearchParams({
      url: url,
      text: title,
    });
    return `https://t.me/share/url?${params.toString()}`;
  },

  email: (url, title, body = '') => {
    const subject = `Check out: ${title}`;
    const emailBody = body || `I thought you might be interested in this: ${title}\n\n${url}`;
    const params = new URLSearchParams({
      subject: subject,
      body: emailBody,
    });
    return `mailto:?${params.toString()}`;
  },

  // Note: Instagram doesn't support direct URL sharing via web links
  // This will copy the content to clipboard for manual sharing
  instagram: (url, title) => {
    return {
      type: 'copy',
      content: `${title}\n\n${url}\n\n#UmugoreUzashimwa #BiblicalPrinciples #WomenInspiration`,
    };
  },
};

/**
 * Open social sharing popup window
 */
export const openShareWindow = (url, title = 'Share', width = 600, height = 400) => {
  if (typeof window === 'undefined') return;

  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;

  const popup = window.open(
    url,
    'share',
    `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
  );

  // Focus the popup window
  if (popup) {
    popup.focus();
  }

  return popup;
};

/**
 * Copy URL to clipboard
 */
export const copyToClipboard = async (text) => {
  if (typeof window === 'undefined') return false;

  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      return success;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Get default hashtags for different content types
 */
export const getDefaultHashtags = (type = 'article') => {
  const commonTags = ['UmugoreUzashimwa', 'BiblicalPrinciples', 'WomenInspiration'];
  
  switch (type) {
    case 'article':
      return [...commonTags, 'ChristianWomen', 'Faith'];
    case 'testimony':
      return [...commonTags, 'Testimony', 'FaithJourney'];
    default:
      return commonTags;
  }
};

/**
 * Generate sharing data for a specific content item
 */
export const generateShareData = (item, type = 'article', baseUrl = '') => {
  const url = type === 'article' 
    ? `${baseUrl}/articles/${item.slug}` 
    : `${baseUrl}/testimonies/${item.id}`;
  
  const title = type === 'article' 
    ? item.title 
    : `${item.name}'s Testimony`;
  
  const description = type === 'article'
    ? (item.content ? item.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...' : '')
    : (item.content ? item.content.substring(0, 160) + '...' : '');

  const hashtags = getDefaultHashtags(type);

  return {
    url,
    title,
    description,
    hashtags,
    image: item.image || null,
  };
};
