// Cache management utilities for development and production
import cache from './cache';

class CacheManager {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  // Clear all in-memory cache
  clearMemoryCache() {
    cache.clear();
    console.log('✅ Memory cache cleared');
  }

  // Clear service worker cache
  async clearServiceWorkerCache() {
    if (typeof window !== 'undefined' && 'caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('✅ Service Worker cache cleared');
        return true;
      } catch (error) {
        console.error('❌ Error clearing Service Worker cache:', error);
        return false;
      }
    }
    return false;
  }

  // Force reload without cache
  forceReload() {
    if (typeof window !== 'undefined') {
      // Clear memory cache first
      this.clearMemoryCache();
      
      // Reload with cache bypass
      window.location.reload(true);
    }
  }

  // Clear all caches and reload
  async clearAllCaches() {
    console.log('🧹 Clearing all caches...');
    
    // Clear memory cache
    this.clearMemoryCache();
    
    // Clear service worker cache
    await this.clearServiceWorkerCache();
    
    // Clear browser cache for this domain
    if (typeof window !== 'undefined') {
      try {
        // Clear localStorage
        localStorage.clear();
        console.log('✅ LocalStorage cleared');
        
        // Clear sessionStorage
        sessionStorage.clear();
        console.log('✅ SessionStorage cleared');
        
      } catch (error) {
        console.error('❌ Error clearing browser storage:', error);
      }
    }
    
    console.log('✅ All caches cleared successfully');
  }

  // Get cache status
  async getCacheStatus() {
    const status = {
      memoryCache: {
        size: cache.size(),
        keys: Array.from(cache.cache.keys())
      },
      serviceWorkerCache: {
        available: 'caches' in window,
        cacheNames: []
      }
    };

    if (typeof window !== 'undefined' && 'caches' in window) {
      try {
        status.serviceWorkerCache.cacheNames = await caches.keys();
      } catch (error) {
        console.error('Error getting cache names:', error);
      }
    }

    return status;
  }

  // Development helper: disable caching
  disableCaching() {
    if (this.isDevelopment) {
      // Override cache.get to always return null
      const originalGet = cache.get.bind(cache);
      cache.get = () => null;
      
      console.log('🚫 Caching disabled for development');
      
      // Return function to restore caching
      return () => {
        cache.get = originalGet;
        console.log('✅ Caching restored');
      };
    }
  }

  // Check if running in development
  isDev() {
    return this.isDevelopment;
  }

  // Add cache busting parameter to URLs
  addCacheBuster(url) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}_cb=${Date.now()}`;
  }
}

// Create singleton instance
const cacheManager = new CacheManager();

// Development helpers - only available in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Add global cache management functions for development
  window.clearCache = () => cacheManager.clearAllCaches();
  window.clearMemoryCache = () => cacheManager.clearMemoryCache();
  window.clearSWCache = () => cacheManager.clearServiceWorkerCache();
  window.getCacheStatus = () => cacheManager.getCacheStatus();
  window.disableCaching = () => cacheManager.disableCaching();
  window.forceReload = () => cacheManager.forceReload();
  
  console.log(`
🛠️  Development Cache Management Available:
• window.clearCache() - Clear all caches
• window.clearMemoryCache() - Clear memory cache only
• window.clearSWCache() - Clear service worker cache
• window.getCacheStatus() - View cache status
• window.disableCaching() - Disable caching temporarily
• window.forceReload() - Force reload without cache
  `);
}

export default cacheManager;
