/**
 * Utility functions for handling HTML content from CKEditor
 */

/**
 * Strip HTML tags and decode HTML entities from text
 * @param {string} html - HTML string to clean
 * @param {number} maxLength - Maximum length of returned text (optional)
 * @returns {string} - Clean text without HTML tags
 */
export function stripHtml(html, maxLength = null) {
  if (!html) return '';
  
  // Remove HTML tags
  let text = html.replace(/<[^>]*>/g, '');
  
  // Decode common HTML entities
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'");
  
  // Clean up extra whitespace
  text = text.replace(/\s+/g, ' ').trim();
  
  // Truncate if maxLength is specified
  if (maxLength && text.length > maxLength) {
    text = text.substring(0, maxLength) + '...';
  }
  
  return text;
}

/**
 * Sanitize HTML content for safe rendering
 * This is a basic sanitizer - for production, consider using a library like DOMPurify
 * @param {string} html - HTML string to sanitize
 * @returns {string} - Sanitized HTML
 */
export function sanitizeHtml(html) {
  if (!html) return '';
  
  // Remove potentially dangerous tags and attributes
  let sanitized = html
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
    .replace(/<object[^>]*>.*?<\/object>/gi, '')
    .replace(/<embed[^>]*>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '') // Remove event handlers
    .replace(/javascript:/gi, ''); // Remove javascript: URLs
  
  return sanitized;
}

/**
 * Extract a clean excerpt from HTML content
 * @param {string} html - HTML content
 * @param {number} length - Desired excerpt length
 * @returns {string} - Clean excerpt
 */
export function extractExcerpt(html, length = 150) {
  return stripHtml(html, length);
}

/**
 * Check if content contains HTML tags
 * @param {string} content - Content to check
 * @returns {boolean} - True if content contains HTML tags
 */
export function containsHtml(content) {
  if (!content) return false;
  return /<[^>]*>/g.test(content);
}
