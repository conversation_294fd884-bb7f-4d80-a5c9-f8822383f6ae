'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FaSearch, FaTimes } from 'react-icons/fa';

export default function SearchModal({ isOpen, onClose }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const router = useRouter();
  const inputRef = useRef(null);

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setIsSearching(true);
      // Navigate to articles page with search query
      router.push(`/articles?search=${encodeURIComponent(searchTerm.trim())}`);
      onClose();
      setSearchTerm('');
      setIsSearching(false);
    }
  };

  const handleClose = () => {
    setSearchTerm('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-modal flex items-start justify-center pt-16 md:pt-24">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-30 backdrop-blur-md"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-background rounded-lg shadow-xl w-full max-w-2xl mx-4 border border-neutral-200">
        <form onSubmit={handleSearch} className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <h2 className="text-xl font-semibold text-neutral-800">Shakisha Inyandiko</h2>
            <button
              type="button"
              onClick={handleClose}
              className="ml-auto p-2 text-neutral-400 hover:text-neutral-600 rounded-full hover:bg-neutral-100"
            >
              <FaTimes />
            </button>
          </div>

          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Shakisha inyandiko, ingingo, cyangwa amagambo..."
              className="w-full pl-12 pr-4 py-4 text-lg border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={isSearching}
            />
            
            <button
              type="submit"
              disabled={!searchTerm.trim() || isSearching}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-primary disabled:opacity-50"
            >
              <FaSearch className="text-xl" />
            </button>
          </div>
          
          <div className="mt-4 flex flex-col sm:flex-row gap-2 justify-between items-start sm:items-center">
            <p className="text-sm text-neutral-600">
              Kanda Enter ushakishe cyangwa Escape ufunge
            </p>
            <button
              type="submit"
              disabled={!searchTerm.trim() || isSearching}
              className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSearching ? 'Irashakisha...' : 'Shakisha Inyandiko'}
            </button>
          </div>
        </form>
        
        {/* Quick suggestions */}
        <div className="border-t border-neutral-200 p-6 bg-neutral-50">
          <h3 className="text-sm font-medium text-neutral-700 mb-3">Ingingo Zikunda</h3>
          <div className="flex flex-wrap gap-2">
            {['Amahame ya Bibiliya', 'Ubugore bw\'iki gihe', 'Urugendo rw\'ukwizera', 'Umuryango', 'Gushishikariza'].map((topic) => (
              <button
                key={topic}
                onClick={() => {
                  setSearchTerm(topic);
                  // Auto-submit after setting the term
                  setTimeout(() => {
                    if (topic.trim()) {
                      router.push(`/articles?search=${encodeURIComponent(topic)}`);
                      onClose();
                      setSearchTerm('');
                    }
                  }, 100);
                }}
                className="px-3 py-1 text-sm bg-white border border-neutral-300 rounded-full hover:bg-primary hover:text-white hover:border-primary transition-colors"
              >
                {topic}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
