'use client';

import { useState } from 'react';
import { FaCheckCircle, FaExclamationTriangle, FaPaperPlane } from 'react-icons/fa';
import { api } from '../lib/api';

export default function TestimonyForm() {
  const [formData, setFormData] = useState({
    name: '',
    title: '',
    email: '',
    content: '',
    image: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      image: file
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage('');

    try {
      // Validate required fields
      if (!formData.name.trim() || !formData.email.trim() || !formData.content.trim()) {
        throw new Error('Uzuza amakuru yose akenewe');
      }

      // Prepare form data for submission
      const submissionData = {
        name: formData.name.trim(),
        title: formData.title.trim() || '',
        email: formData.email.trim(),
        content: formData.content.trim(),
        image: formData.image // For now, we'll handle image upload separately if needed
      };

      // Submit to API
      await api.testimonies.create(submissionData);

      // Success
      setIsSuccess(true);
      setMessage('Murakoze! Ubuhamya bwanyu bwakiriwe neza.');

      // Reset form
      setFormData({
        name: '',
        title: '',
        email: '',
        content: '',
        image: null
      });

      // Reset file input
      const fileInput = document.getElementById('image');
      if (fileInput) {
        fileInput.value = '';
      }

      // Scroll to top of form to show success message
      setTimeout(() => {
        const formElement = document.querySelector('form');
        if (formElement) {
          formElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);

    } catch (error) {
      setIsSuccess(false);
      setMessage(error.message || 'Habaye ikosa. Ongera ugerageze nyuma.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-background p-8 rounded-lg border border-secondary-dark text-left">
      <div className="mb-6">
        <label htmlFor="name" className="block text-primary font-medium mb-2">
          Amazina yawe <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          className="w-full px-4 py-2 border border-secondary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Injiza izina ryawe"
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="mb-6">
        <label htmlFor="title" className="block text-primary font-medium mb-2">
          Uwo uriwe / Icyo ukora
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          className="w-full px-4 py-2 border border-secondary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Urugero: umubyeyi, umucuruzi, umunyeshuri"
          disabled={isSubmitting}
        />
      </div>

      <div className="mb-6">
        <label htmlFor="email" className="block text-primary font-medium mb-2">
          Aderesi ya email <span className="text-red-500">*</span>
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          className="w-full px-4 py-2 border border-secondary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Injiza email yawe"
          required
          disabled={isSubmitting}
        />
        <p className="text-sm text-primary-dark mt-1">
          Mugihe twifuza gushyira kumugaragaro ubuhamya bwanyu, tuzabavugisha
        </p>
      </div>

      <div className="mb-6">
        <label htmlFor="content" className="block text-primary font-medium mb-2">
          Ubuhamya <span className="text-red-500">*</span>
        </label>
        <textarea
          id="content"
          name="content"
          value={formData.content}
          onChange={handleInputChange}
          rows="6"
          className="w-full px-4 py-2 border border-secondary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Dusangize inkuru yawe..."
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="mb-6">
        <label htmlFor="image" className="block text-primary font-medium mb-2">
          Ifoto yawe (Ntabwo ari ngombwa)
        </label>
        <input
          type="file"
          id="image"
          name="image"
          onChange={handleImageChange}
          accept="image/*"
          className="w-full px-4 py-2 border border-secondary-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          disabled={isSubmitting}
        />
        <p className="text-sm text-primary-dark mt-1">
          Hitamo ifoto yawe niba ushaka (JPEG, PNG, max 5MB)
        </p>
      </div>

      {message && (
        <div className={`mb-6 p-6 rounded-lg transition-all duration-500 ease-in-out transform ${isSuccess ? 'bg-green-50 text-green-800 border-2 border-green-200 animate-pulse' : 'bg-red-50 text-red-800 border-2 border-red-200'}`}>
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              {isSuccess ? (
                <FaCheckCircle className="text-green-600 text-xl animate-bounce" />
              ) : (
                <FaExclamationTriangle className="text-red-600 text-xl" />
              )}
            </div>
            <div className="flex-1">
              <h4 className={`font-semibold mb-1 ${isSuccess ? 'text-green-800' : 'text-red-800'}`}>
                {isSuccess ? 'Ubuhamya bwakiriwe neza!' : 'Habaye ikosa'}
              </h4>
              <p className={`text-sm ${isSuccess ? 'text-green-700' : 'text-red-700'}`}>
                {message}
              </p>
              {isSuccess && (
                <div className="mt-3 text-xs text-green-600 space-y-1">
                  <p className="flex items-center gap-2">
                    <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                    Tuzaburebera hanyuma tukabushyira kumugaragaro
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                    Tuzabavugisha kuri email yanyu niba dukeneye amakuru y'inyongera
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                    Murakoze cyane ko mwasangiye ubuhamya bwanyu natwe
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <button
        type="submit"
        className="btn btn-primary w-full md:w-auto flex items-center justify-center gap-2"
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
            Iyohereza...
          </>
        ) : (
          <>
            <FaPaperPlane />
            Ohereza ubuhamya bwawe
          </>
        )}
      </button>
    </form>
  );
}
