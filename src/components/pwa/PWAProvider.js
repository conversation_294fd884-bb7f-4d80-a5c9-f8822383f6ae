'use client';

import { useEffect } from 'react';
import InstallPrompt from './InstallPrompt';
import { registerServiceWorker } from '../../lib/serviceWorker';

export default function PWAProvider({ children }) {
  useEffect(() => {
    // Register service worker for caching and offline functionality
    registerServiceWorker();

    // Add PWA meta tags dynamically for better iOS support
    const addMetaTags = () => {
      const metaTags = [
        { name: 'apple-mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
        { name: 'apple-mobile-web-app-title', content: '<PERSON><PERSON><PERSON>' },
        { name: 'mobile-web-app-capable', content: 'yes' },
        { name: 'theme-color', content: '#A8B9A3' },
        { name: 'msapplication-TileColor', content: '#A8B9A3' },
        { name: 'msapplication-config', content: '/browserconfig.xml' }
      ];

      metaTags.forEach(tag => {
        const existingTag = document.querySelector(`meta[name="${tag.name}"]`);
        if (!existingTag) {
          const meta = document.createElement('meta');
          meta.name = tag.name;
          meta.content = tag.content;
          document.head.appendChild(meta);
        }
      });

      // Add apple touch icons
      const iconSizes = [57, 60, 72, 76, 114, 120, 144, 152, 180];
      iconSizes.forEach(size => {
        const existingIcon = document.querySelector(`link[rel="apple-touch-icon"][sizes="${size}x${size}"]`);
        if (!existingIcon) {
          const link = document.createElement('link');
          link.rel = 'apple-touch-icon';
          link.sizes = `${size}x${size}`;
          link.href = `/icons/icon-${size >= 192 ? 192 : size >= 152 ? 152 : size >= 144 ? 144 : size >= 128 ? 128 : size >= 96 ? 96 : 72}x${size >= 192 ? 192 : size >= 152 ? 152 : size >= 144 ? 144 : size >= 128 ? 128 : size >= 96 ? 96 : 72}.png`;
          document.head.appendChild(link);
        }
      });

      // Add startup images for iOS
      const startupImages = [
        { media: '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)', href: '/icons/icon-640x1136.png' },
        { media: '(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)', href: '/icons/icon-750x1334.png' },
        { media: '(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)', href: '/icons/icon-1242x2208.png' },
        { media: '(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)', href: '/icons/icon-1125x2436.png' }
      ];

      startupImages.forEach(image => {
        const existingImage = document.querySelector(`link[rel="apple-touch-startup-image"][media="${image.media}"]`);
        if (!existingImage) {
          const link = document.createElement('link');
          link.rel = 'apple-touch-startup-image';
          link.media = image.media;
          link.href = image.href;
          document.head.appendChild(link);
        }
      });
    };

    addMetaTags();

    // Handle app updates
    const handleAppUpdate = () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          // Reload the page when a new service worker takes control
          window.location.reload();
        });
      }
    };

    handleAppUpdate();

    // Add to home screen prompt handling for iOS
    const handleIOSInstallPrompt = () => {
      // Check if it's iOS and not already installed
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                          window.navigator.standalone === true;
      
      if (isIOS && !isStandalone) {
        // Store install prompt availability
        window.addEventListener('beforeinstallprompt', (e) => {
          e.preventDefault();
          window.deferredPrompt = e;
        });
      }
    };

    handleIOSInstallPrompt();

    // Handle online/offline status
    const handleOnlineStatus = () => {
      const updateOnlineStatus = () => {
        if (navigator.onLine) {
          // App is online - sync any pending data
          console.log('App is online');
        } else {
          // App is offline - show offline indicator if needed
          console.log('App is offline');
        }
      };

      window.addEventListener('online', updateOnlineStatus);
      window.addEventListener('offline', updateOnlineStatus);

      return () => {
        window.removeEventListener('online', updateOnlineStatus);
        window.removeEventListener('offline', updateOnlineStatus);
      };
    };

    const cleanupOnlineStatus = handleOnlineStatus();

    return () => {
      if (cleanupOnlineStatus) {
        cleanupOnlineStatus();
      }
    };
  }, []);

  return (
    <>
      {children}
      <InstallPrompt />
    </>
  );
}
