'use client';

import { useState, useEffect } from 'react';
import { FaTimes, FaDownload, FaShare, FaPlus } from 'react-icons/fa';

export default function InstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  useEffect(() => {
    // Check if running on iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // Check if already installed (standalone mode)
    const standalone = window.matchMedia('(display-mode: standalone)').matches || 
                      window.navigator.standalone === true;
    setIsStandalone(standalone);

    // Check if user has already dismissed the prompt
    const dismissed = sessionStorage.getItem('pwa-install-dismissed');
    
    if (!standalone && !dismissed) {
      // For Android/Desktop - listen for beforeinstallprompt
      const handleBeforeInstallPrompt = (e) => {
        e.preventDefault();
        setDeferredPrompt(e);
        
        // Show prompt after user has interacted with the site
        if (hasInteracted) {
          setTimeout(() => setShowPrompt(true), 1000);
        }
      };

      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

      // For iOS - show manual instructions after interaction
      if (iOS) {
        const timer = setTimeout(() => {
          if (hasInteracted) {
            setShowPrompt(true);
          }
        }, 3000);
        
        return () => clearTimeout(timer);
      }

      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      };
    }
  }, [hasInteracted]);

  // Track user interaction
  useEffect(() => {
    const handleInteraction = () => {
      setHasInteracted(true);
    };

    // Listen for various interaction events
    const events = ['click', 'scroll', 'keydown', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleInteraction, { once: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleInteraction);
      });
    };
  }, []);

  const handleInstall = async () => {
    if (deferredPrompt) {
      // Android/Desktop installation
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('PWA installed');
      }
      
      setDeferredPrompt(null);
      setShowPrompt(false);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  };

  if (!showPrompt || isStandalone) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50">
      <div className="bg-background border border-secondary-dark rounded-lg shadow-lg p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">UU</span>
            </div>
            <div>
              <h3 className="font-semibold text-primary text-sm">Umugore Uzashimwa</h3>
              <p className="text-xs text-primary-dark">Shyiraho App</p>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="text-primary-dark hover:text-primary p-1"
            aria-label="Close"
          >
            <FaTimes className="w-4 h-4" />
          </button>
        </div>

        {isIOS ? (
          // iOS Instructions
          <div className="space-y-3">
            <p className="text-sm text-primary-dark">
              Shyiraho app kuri telefoni yawe:
            </p>
            <div className="space-y-2 text-xs text-primary-dark">
              <div className="flex items-center gap-2">
                <span className="w-5 h-5 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xs">1</span>
                <span>Kanda <FaShare className="inline w-3 h-3 mx-1" /> (Share) hasi</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-5 h-5 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xs">2</span>
                <span>Hitamo "Add to Home Screen" <FaPlus className="inline w-3 h-3 mx-1" /></span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-5 h-5 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xs">3</span>
                <span>Kanda "Add" kugirango urangize</span>
              </div>
            </div>
          </div>
        ) : (
          // Android/Desktop Installation
          <div className="space-y-3">
            <p className="text-sm text-primary-dark">
              Shyiraho app kugirango ubone byoroshye kandi ukore offline.
            </p>
            <button
              onClick={handleInstall}
              className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary-dark transition-colors flex items-center justify-center gap-2 text-sm font-medium"
            >
              <FaDownload className="w-4 h-4" />
              Shyiraho App
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
