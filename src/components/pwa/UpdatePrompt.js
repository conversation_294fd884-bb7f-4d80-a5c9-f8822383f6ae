'use client';

import { useState, useEffect } from 'react';
import { FaDownload, FaTimes, FaSync } from 'react-icons/fa';

export default function UpdatePrompt() {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [registration, setRegistration] = useState(null);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      // Listen for service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // New service worker has taken control
        if (updateAvailable) {
          window.location.reload();
        }
      });

      // Check for updates periodically
      const checkForUpdates = async () => {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          if (registration) {
            setRegistration(registration);
            
            // Check if there's a waiting service worker
            if (registration.waiting) {
              setUpdateAvailable(true);
              setShowUpdatePrompt(true);
            }

            // Listen for new service worker installing
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New service worker is available
                    setUpdateAvailable(true);
                    setShowUpdatePrompt(true);
                  }
                });
              }
            });
          }
        } catch (error) {
          console.error('Error checking for updates:', error);
        }
      };

      checkForUpdates();

      // Check for updates every 30 minutes
      const updateInterval = setInterval(checkForUpdates, 30 * 60 * 1000);

      return () => {
        clearInterval(updateInterval);
      };
    }
  }, [updateAvailable]);

  const handleUpdate = async () => {
    if (!registration || !registration.waiting) return;

    setIsUpdating(true);

    try {
      // Tell the waiting service worker to skip waiting and become active
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      
      // The page will reload automatically when the new service worker takes control
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error updating app:', error);
      setIsUpdating(false);
    }
  };

  const handleDismiss = () => {
    setShowUpdatePrompt(false);
    // Show again in 1 hour
    setTimeout(() => {
      if (updateAvailable) {
        setShowUpdatePrompt(true);
      }
    }, 60 * 60 * 1000);
  };

  if (!showUpdatePrompt) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto">
      <div className="bg-white border border-primary rounded-lg shadow-lg p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center">
            <div className="bg-primary text-white rounded-full p-2 mr-3">
              <FaSync className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold text-primary">Hari Impinduka</h3>
              <p className="text-sm text-neutral-600">New App Update Available</p>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="text-neutral-400 hover:text-neutral-600 p-1"
            aria-label="Dismiss"
          >
            <FaTimes className="w-4 h-4" />
          </button>
        </div>

        <p className="text-sm text-neutral-700 mb-4">
          Hari impinduka. Kanda "Update".
        </p>
        <p className="text-xs text-neutral-500 mb-4">
          A new version with improvements and new features is available. Click "Update" to get the latest version.
        </p>

        <div className="flex gap-2">
          <button
            onClick={handleUpdate}
            disabled={isUpdating}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              isUpdating
                ? 'bg-neutral-100 text-neutral-400 cursor-not-allowed'
                : 'bg-primary text-white hover:bg-primary-dark active:bg-primary-dark'
            }`}
          >
            {isUpdating ? (
              <>
                <FaSync className="w-4 h-4 animate-spin" />
                Guhindura...
              </>
            ) : (
              <>
                <FaDownload className="w-4 h-4" />
                Update
              </>
            )}
          </button>
          
          <button
            onClick={handleDismiss}
            className="px-4 py-2 text-neutral-600 hover:text-neutral-800 font-medium transition-colors"
          >
            Nyuma
          </button>
        </div>

        <div className="mt-3 pt-3 border-t border-neutral-100">
          <p className="text-xs text-neutral-500 text-center">
            Iyo ukoze Update, App irongera gutangira kugira ngo ubone impinduka nshya.
          </p>
          <p className="text-xs text-neutral-400 text-center mt-1">
            After updating, the app will restart to show the new features.
          </p>
        </div>
      </div>
    </div>
  );
}
