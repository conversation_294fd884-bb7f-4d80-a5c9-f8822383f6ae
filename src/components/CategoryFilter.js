'use client';

import { useState, useEffect } from 'react';
import { FaFilter, FaTimes } from 'react-icons/fa';

export default function CategoryFilter({ 
  categories = [], 
  selectedCategories = [], 
  onCategoryChange,
  className = '' 
}) {
  const [isOpen, setIsOpen] = useState(false);

  const handleCategoryToggle = (categorySlug) => {
    const newSelected = selectedCategories.includes(categorySlug)
      ? selectedCategories.filter(slug => slug !== categorySlug)
      : [...selectedCategories, categorySlug];
    
    onCategoryChange(newSelected);
  };

  const clearAllFilters = () => {
    onCategoryChange([]);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Filter button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50"
      >
        <FaFilter className="text-sm" />
        Inyandiko mu ibyiciro
        {selectedCategories.length > 0 && (
          <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">
            {selectedCategories.length}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-background border border-secondary-dark rounded-lg shadow-lg z-10">
          <div className="p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium">Hitamo mu byiciro</h3>
              {selectedCategories.length > 0 && (
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-accent hover:text-accent-dark"
                >
                  Siba byose
                </button>
              )}
            </div>
            
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {categories.map((category) => (
                <label
                  key={category.slug}
                  className="flex items-center gap-2 cursor-pointer hover:bg-secondary p-2 rounded"
                >
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category.slug)}
                    onChange={() => handleCategoryToggle(category.slug)}
                    className="rounded border-secondary-dark text-primary focus:ring-primary"
                  />
                  <span className="text-sm">{category.name}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
