'use client';

import { useState } from 'react';
import { FaSearch, FaTimes } from 'react-icons/fa';

export default function SearchBar({ 
  onSearch, 
  placeholder = 'Search...', 
  initialValue = '',
  className = '' 
}) {
  const [searchTerm, setSearchTerm] = useState(initialValue);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch(searchTerm.trim());
  };

  const handleClear = () => {
    setSearchTerm('');
    onSearch('');
  };

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-3 border border-secondary-dark rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background"
        />
        
        {/* Search icon */}
        <button
          type="submit"
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary-dark hover:text-primary"
        >
          <FaSearch />
        </button>
        
        {/* Clear button */}
        {searchTerm && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary-dark hover:text-accent"
          >
            <FaTimes />
          </button>
        )}
      </div>
    </form>
  );
}
