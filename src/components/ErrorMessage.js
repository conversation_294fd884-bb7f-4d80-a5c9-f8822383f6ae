import { FaExclamationTriangle, FaRedo } from 'react-icons/fa';

export default function ErrorMessage({ 
  message = 'Something went wrong. Please try again.', 
  onRetry = null,
  className = '' 
}) {
  return (
    <div className={`bg-accent-light border border-accent rounded-lg p-6 text-center ${className}`}>
      <FaExclamationTriangle className="text-accent text-3xl mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-accent-dark mb-2">Error</h3>
      <p className="text-accent-dark mb-4">{message}</p>
      {onRetry && (
        <button 
          onClick={onRetry}
          className="inline-flex items-center gap-2 bg-accent text-white px-4 py-2 rounded-lg hover:bg-accent-dark transition-colors"
        >
          <FaRedo className="text-sm" />
          Try Again
        </button>
      )}
    </div>
  );
}
