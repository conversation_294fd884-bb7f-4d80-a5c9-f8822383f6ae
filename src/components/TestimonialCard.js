import Link from 'next/link';
import { stripHtml } from '../lib/htmlUtils';

export default function TestimonialCard({ testimonial, showFullContent = false, isHomepage = false }) {
  // Handle both API data structure and legacy props
  const {
    id,
    name,
    email,
    content,
    image,
    status,
    created_at,
    // Legacy props for backward compatibility
    quote,
    author,
  } = testimonial || {};

  // Extract data with fallbacks and strip HTML tags
  const rawContent = content || quote || 'The wisdom I\'ve found through this community has transformed my approach to balancing my career, family, and faith. I\'m grateful for the practical guidance rooted in biblical principles.';
  const fullContent = stripHtml(rawContent);

  // Truncate content for card view - use different logic for homepage vs other pages
  let testimonialContent;
  if (isHomepage) {
    // For homepage, show only first 150 characters
    testimonialContent = fullContent.length > 150 ? fullContent.substring(0, 150) + '...' : fullContent;
  } else if (showFullContent) {
    // For detail pages or when explicitly requested, show full content
    testimonialContent = fullContent;
  } else {
    // For list pages, show first 150 characters
    testimonialContent = fullContent.length > 150 ? fullContent.substring(0, 150) + '...' : fullContent;
  }

  const testimonialAuthor = {
    name: name || author?.name || 'Rebecca Thomas',
    title: author?.title || 'Mwene Data Ukundwa',
    image: image || author?.image || null,
  };

  const displayDate = created_at ? new Date(created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : null;

  return (
    <div className="bg-background p-8 rounded-lg border border-secondary-dark">
      <div className="text-accent text-5xl font-serif mb-4">"</div>
      <p className="text-lg mb-6 text-primary-dark text-justify leading-relaxed">{testimonialContent}</p>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-secondary mr-4 relative overflow-hidden">
            {testimonialAuthor.image ? (
              <img
                src={testimonialAuthor.image}
                alt={testimonialAuthor.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center text-primary text-lg font-semibold">
                {testimonialAuthor.name.charAt(0)}
              </div>
            )}
          </div>
          <div>
            <div className="font-medium">{testimonialAuthor.name}</div>
            <div className="text-sm text-primary-dark">{testimonialAuthor.title}</div>
          </div>
        </div>

        {displayDate && (
          <div className="text-sm text-primary-dark">
            {displayDate}
          </div>
        )}
      </div>

      {id && (
        <div className="mt-4 pt-4 border-t border-secondary-dark">
          <Link
            href={`/testimonies/${id}`}
            className="text-primary text-sm font-medium hover:text-primary-dark"
          >
            Soma ubuhamya bwose
          </Link>
        </div>
      )}
    </div>
  );
}
