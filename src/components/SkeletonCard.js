// Skeleton loading components for different card types

export function ArticleCardSkeleton() {
  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark animate-pulse">
      {/* Image skeleton */}
      <div className="h-48 bg-secondary"></div>

      <div className="p-6">
        {/* Category skeleton */}
        <div className="h-4 bg-secondary rounded w-20 mb-2"></div>

        {/* Title skeleton */}
        <div className="h-6 bg-secondary rounded w-full mb-2"></div>
        <div className="h-6 bg-secondary rounded w-3/4 mb-4"></div>

        {/* Excerpt skeleton */}
        <div className="h-4 bg-secondary rounded w-full mb-2"></div>
        <div className="h-4 bg-secondary rounded w-5/6 mb-4"></div>

        {/* Author section skeleton */}
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-secondary mr-3"></div>
          <div>
            <div className="h-4 bg-secondary rounded w-24 mb-1"></div>
            <div className="h-3 bg-secondary rounded w-16"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function TestimonialCardSkeleton() {
  return (
    <div className="bg-background p-8 rounded-lg border border-secondary-dark animate-pulse">
      {/* Quote mark skeleton */}
      <div className="h-12 w-8 bg-secondary rounded mb-4"></div>

      {/* Content skeleton */}
      <div className="mb-6">
        <div className="h-4 bg-secondary rounded w-full mb-2"></div>
        <div className="h-4 bg-secondary rounded w-full mb-2"></div>
        <div className="h-4 bg-secondary rounded w-3/4 mb-2"></div>
      </div>

      {/* Author section skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-secondary mr-4"></div>
          <div>
            <div className="h-4 bg-secondary rounded w-24 mb-1"></div>
            <div className="h-3 bg-secondary rounded w-20"></div>
          </div>
        </div>
        <div className="h-3 bg-secondary rounded w-16"></div>
      </div>
    </div>
  );
}

export function PartnerCardSkeleton() {
  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark animate-pulse">
      {/* Logo section skeleton */}
      <div className="h-32 bg-secondary"></div>

      <div className="p-6">
        {/* Categories skeleton */}
        <div className="flex flex-wrap gap-2 mb-3">
          <div className="h-5 bg-secondary rounded-full w-16"></div>
          <div className="h-5 bg-secondary rounded-full w-20"></div>
        </div>

        {/* Name skeleton */}
        <div className="h-6 bg-secondary rounded w-3/4 mb-2"></div>

        {/* Description skeleton */}
        <div className="mb-4">
          <div className="h-4 bg-secondary rounded w-full mb-2"></div>
          <div className="h-4 bg-secondary rounded w-5/6"></div>
        </div>

        {/* Actions skeleton */}
        <div className="flex justify-between items-center">
          <div className="h-4 bg-secondary rounded w-20"></div>
          <div className="h-4 bg-secondary rounded w-24"></div>
        </div>
      </div>
    </div>
  );
}

export function AuthorCardSkeleton() {
  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark animate-pulse">
      <div className="p-6">
        {/* Author header skeleton */}
        <div className="flex items-start gap-4 mb-4">
          <div className="w-16 h-16 rounded-full bg-secondary flex-shrink-0"></div>
          <div className="flex-1">
            <div className="h-6 bg-secondary rounded w-3/4 mb-1"></div>
            <div className="h-4 bg-secondary rounded w-16"></div>
          </div>
        </div>

        {/* Bio skeleton */}
        <div className="mb-4">
          <div className="h-4 bg-secondary rounded w-full mb-2"></div>
          <div className="h-4 bg-secondary rounded w-5/6 mb-2"></div>
          <div className="h-4 bg-secondary rounded w-3/4"></div>
        </div>

        {/* Articles skeleton */}
        <div className="mb-4">
          <div className="h-4 bg-secondary rounded w-24 mb-2"></div>
          <div className="space-y-1">
            <div className="h-3 bg-secondary rounded w-full"></div>
            <div className="h-3 bg-secondary rounded w-4/5"></div>
            <div className="h-3 bg-secondary rounded w-3/4"></div>
          </div>
        </div>

        {/* View profile link skeleton */}
        <div className="pt-4 border-t border-secondary-dark">
          <div className="h-4 bg-secondary rounded w-20"></div>
        </div>
      </div>
    </div>
  );
}

export function CategoryCardSkeleton() {
  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark animate-pulse">
      <div className="p-6">
        {/* Icon and title skeleton */}
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-secondary rounded-lg mr-4"></div>
          <div className="h-6 bg-secondary rounded w-3/4"></div>
        </div>

        {/* Description skeleton */}
        <div className="mb-4">
          <div className="h-4 bg-secondary rounded w-full mb-2"></div>
          <div className="h-4 bg-secondary rounded w-5/6"></div>
        </div>

        {/* Footer skeleton */}
        <div className="flex justify-between items-center">
          <div className="h-4 bg-secondary rounded w-24"></div>
          <div className="h-3 bg-secondary rounded w-16"></div>
        </div>
      </div>
    </div>
  );
}

// Grid skeletons for multiple cards
export function ArticleGridSkeleton({ count = 6 }) {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: count }, (_, i) => (
        <ArticleCardSkeleton key={i} />
      ))}
    </div>
  );
}

export function TestimonialGridSkeleton({ count = 4 }) {
  return (
    <div className="grid md:grid-cols-2 gap-8">
      {Array.from({ length: count }, (_, i) => (
        <TestimonialCardSkeleton key={i} />
      ))}
    </div>
  );
}

export function PartnerGridSkeleton({ count = 6 }) {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: count }, (_, i) => (
        <PartnerCardSkeleton key={i} />
      ))}
    </div>
  );
}

export function AuthorGridSkeleton({ count = 6 }) {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: count }, (_, i) => (
        <AuthorCardSkeleton key={i} />
      ))}
    </div>
  );
}
