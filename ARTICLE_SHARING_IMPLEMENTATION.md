# Article Sharing with Images Implementation

This document explains how article sharing with images is implemented in the Umugore Uzashimwa website, including Open Graph meta tags, social media sharing, and image handling.

## 🎯 Overview

The article sharing system enables users to share articles and testimonies across various social media platforms with proper image previews, titles, and descriptions. The implementation includes:

- **Open Graph Meta Tags**: For rich social media previews
- **Twitter Cards**: Optimized Twitter sharing
- **Social Media Buttons**: Direct sharing to multiple platforms
- **Image Handling**: Automatic image selection and fallbacks
- **Native Sharing**: Mobile-optimized sharing experience

## 📁 File Structure

```
src/
├── components/
│   └── ShareButtons.js          # Main sharing component
├── lib/
│   ├── socialShare.js          # Social sharing utilities
│   └── constants.js            # Site configuration
└── app/
    ├── articles/[slug]/page.js  # Article page with meta tags
    ├── testimonies/[id]/page.js # Testimony page with meta tags
    └── layout.js               # Global meta tags
```

## 🔧 Core Components

### 1. ShareButtons Component (`src/components/ShareButtons.js`)

The main component that renders social sharing buttons and handles sharing logic.

**Key Features:**
- Platform-specific sharing URLs
- Native mobile sharing support
- Copy-to-clipboard functionality
- Instagram content preparation
- Compact and full display modes

**Usage:**
```jsx
<ShareButtons
  item={article}
  type="article"
  title="Sangiza Inyandiko"
  compact={false}
/>
```

**Supported Platforms:**
- Facebook
- Twitter/X
- LinkedIn
- WhatsApp
- Telegram
- Email
- Instagram (copy content)
- Native mobile sharing

### 2. Social Share Utilities (`src/lib/socialShare.js`)

Utility functions for generating platform-specific sharing URLs and handling sharing logic.

**Key Functions:**

#### `socialShareUrls`
Generates sharing URLs for different platforms:
```javascript
socialShareUrls.facebook(url, title)
socialShareUrls.twitter(url, title, hashtags)
socialShareUrls.linkedin(url, title, summary)
socialShareUrls.whatsapp(url, title)
socialShareUrls.telegram(url, title)
socialShareUrls.email(url, title, body)
socialShareUrls.instagram(url, title) // Returns copy content
```

#### `generateShareData(item, type, baseUrl)`
Creates standardized sharing data:
```javascript
{
  url: "https://umugoreuzashimwa.org/articles/article-slug",
  title: "Article Title",
  description: "Article description...",
  hashtags: ["UmugoreUzashimwa", "BiblicalPrinciples"],
  image: "https://api.umugoreuzashimwa.org/media/image.jpg"
}
```

#### `copyToClipboard(text)`
Cross-browser clipboard functionality with fallbacks.

#### `openShareWindow(url, title, width, height)`
Opens sharing popups with optimal dimensions.

## 🖼️ Image Handling

### Image Priority System

The system uses a cascading approach to select the best image for sharing:

1. **Article Featured Image**: `article.featured_image`
2. **Article Image**: `article.image`
3. **Site Default Image**: `SITE_CONFIG.defaultImage`
4. **Fallback Logo**: `/icons/no-bg-logo.png`

### Image Processing

```javascript
// Strip HTML and get image from content
const imageUrl = article.featured_image || 
                 article.image || 
                 SITE_CONFIG.defaultImage;

// Ensure absolute URL for social sharing
const absoluteImageUrl = imageUrl.startsWith('http') 
  ? imageUrl 
  : `${SITE_CONFIG.url}${imageUrl}`;
```

## 📱 Open Graph Implementation

### Article Pages (`src/app/articles/[slug]/page.js`)

Each article page generates dynamic Open Graph meta tags:

```javascript
export async function generateMetadata({ params }) {
  const { article } = await getArticleData(params.slug);
  
  return {
    title: `${article.title} - Umugore Uzashimwa`,
    description: stripHtml(article.short_description || article.content),
    openGraph: {
      title: article.title,
      description: stripHtml(article.short_description || article.content),
      url: `${SITE_CONFIG.url}/articles/${params.slug}`,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: article.featured_image || article.image || SITE_CONFIG.defaultImage,
          width: 1200,
          height: 630,
          alt: article.title,
        },
      ],
      locale: 'rw_RW',
      type: 'article',
      publishedTime: article.created_at,
      authors: article.authors?.map(author => author.name) || [],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: stripHtml(article.short_description || article.content),
      images: [article.featured_image || article.image || SITE_CONFIG.defaultImage],
    },
  };
}
```

### Key Meta Tags Generated

```html
<!-- Open Graph -->
<meta property="og:title" content="Article Title" />
<meta property="og:description" content="Article description..." />
<meta property="og:image" content="https://api.umugoreuzashimwa.org/media/image.jpg" />
<meta property="og:url" content="https://umugoreuzashimwa.org/articles/slug" />
<meta property="og:type" content="article" />
<meta property="og:site_name" content="Umugore Uzashimwa Ministry" />

<!-- Twitter Cards -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="Article Title" />
<meta name="twitter:description" content="Article description..." />
<meta name="twitter:image" content="https://api.umugoreuzashimwa.org/media/image.jpg" />
```

## 🔄 Content Processing

### HTML Stripping

Articles from the CKEditor API contain HTML tags that need to be cleaned for social sharing:

```javascript
// Utility function to strip HTML tags
const stripHtml = (html, maxLength = 160) => {
  if (!html) return '';
  
  const text = html.replace(/<[^>]*>/g, '');
  return maxLength ? text.substring(0, maxLength) + '...' : text;
};
```

### Description Generation

```javascript
const description = article.short_description
  ? stripHtml(article.short_description)
  : (article.content ? stripHtml(article.content, 160) + '...' : 'Read this inspiring article from Umugore Uzashimwa.');
```

## 📲 Platform-Specific Features

### Facebook Sharing
- Uses Facebook Sharer API
- Automatically pulls Open Graph data
- Supports image, title, and description

### Twitter/X Sharing
- Uses Twitter Web Intent API
- Includes hashtags: `#UmugoreUzashimwa #BiblicalPrinciples`
- Optimized for Twitter Cards

### LinkedIn Sharing
- Professional sharing format
- Includes summary and professional hashtags
- Optimized for business networking

### WhatsApp Sharing
- Mobile-optimized
- Combines title and URL in message
- Works on both mobile and desktop

### Instagram Sharing
- Copies formatted content to clipboard
- Includes hashtags and URL
- User manually pastes in Instagram

### Native Mobile Sharing
- Uses Web Share API when available
- Provides native mobile sharing experience
- Fallback to custom buttons on unsupported devices

## 🎨 Styling and UX

### Responsive Design
- Compact mode for mobile/cards
- Full mode for article pages
- Touch-friendly button sizes

### Visual Feedback
- Loading states during sharing
- Success indicators for copy actions
- Platform-specific colors and icons

### Accessibility
- Proper ARIA labels
- Keyboard navigation support
- Screen reader friendly

## 🧪 Testing Sharing

### Tools for Testing
1. **Facebook Debugger**: https://developers.facebook.com/tools/debug/
2. **Twitter Card Validator**: https://cards-dev.twitter.com/validator
3. **LinkedIn Post Inspector**: https://www.linkedin.com/post-inspector/
4. **Open Graph Preview**: Various online tools

### Testing Checklist
- [ ] Images display correctly on all platforms
- [ ] Titles are properly formatted
- [ ] Descriptions are clean (no HTML)
- [ ] URLs are absolute and accessible
- [ ] Mobile sharing works on iOS/Android
- [ ] Copy functionality works across browsers

## 🔧 Configuration

### Site Configuration (`src/lib/constants.js`)
```javascript
export const SITE_CONFIG = {
  name: 'Umugore Uzashimwa Ministry',
  url: 'https://umugoreuzashimwa.org',
  defaultImage: '/icons/no-bg-logo.png',
  // ... other config
};
```

### Hashtag Configuration
```javascript
export const getDefaultHashtags = (type = 'article') => {
  const commonTags = ['UmugoreUzashimwa', 'BiblicalPrinciples', 'WomenInspiration'];
  
  switch (type) {
    case 'article':
      return [...commonTags, 'ChristianWomen', 'Faith'];
    case 'testimony':
      return [...commonTags, 'Testimony', 'FaithJourney'];
    default:
      return commonTags;
  }
};
```

## 🚀 Future Enhancements

### Planned Features
- [ ] Share analytics tracking
- [ ] Custom share images generation
- [ ] Pinterest sharing support
- [ ] Share count display
- [ ] Scheduled sharing
- [ ] Share templates for different content types

### Performance Optimizations
- [ ] Image optimization for social sharing
- [ ] Lazy loading of share buttons
- [ ] Caching of share data
- [ ] CDN integration for images

## 📝 Implementation Notes

### Key Considerations
1. **Image URLs**: Must be absolute URLs for social platforms
2. **Content Length**: Descriptions should be 160 characters or less
3. **HTML Cleaning**: Remove all HTML tags from descriptions
4. **Mobile Experience**: Prioritize native sharing on mobile devices
5. **Fallbacks**: Always provide fallback images and content

### Common Issues and Solutions
1. **Images not showing**: Ensure absolute URLs and proper CORS headers
2. **HTML in descriptions**: Use stripHtml utility function
3. **Mobile sharing not working**: Check Web Share API support
4. **Copy functionality failing**: Implement clipboard fallbacks

This implementation provides a comprehensive, user-friendly sharing experience that works across all major platforms and devices while maintaining the ministry's branding and message consistency.
