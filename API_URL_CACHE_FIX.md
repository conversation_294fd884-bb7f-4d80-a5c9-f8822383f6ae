# API URL Cache Fix Documentation

## Problem Summary
The deployed Vercel site was still hitting the old API URL (`https://umugore-uzashimwa-backend.onrender.com`) despite the code being updated to use the new URL (`https://api.umugoreuzashimwa.org`). This was caused by multiple layers of caching.

## Root Cause Analysis
The issue was caused by **multiple caching layers**:

1. **Service Worker Cache** - Cached API responses from old URL
2. **Vercel Edge Cache** - CDN cached responses from old URL  
3. **Browser Cache** - User browsers cached old responses
4. **Next.js Build Cache** - Build artifacts contained cached data

## Solution Implemented

### 1. Service Worker Updates
- **File**: `public/sw.js`
- **Changes**: 
  - Updated cache version to force refresh
  - Modified fetch strategy to prioritize network over cache for API requests
  - Added comment indicating API URL change

### 2. API Cache Busting
- **File**: `src/lib/api.js`
- **Changes**:
  - Added version parameter to all API requests
  - Forces fresh requests from new API URL
  - Maintains development cache busting

### 3. Cache Management Scripts
- **New File**: `scripts/force-cache-refresh.js`
- **Purpose**: Comprehensive cache clearing for API URL changes
- **Features**:
  - Updates service worker cache version
  - Updates API cache buster version
  - Updates cache-bust manifest
  - Clears Next.js build cache

### 4. Package.json Scripts
- **Added**: `force-cache-refresh` - Run cache refresh script
- **Added**: `build-fresh` - Cache refresh + build in one command

### 5. Documentation Updates
- **File**: `README.md`
- **Added**: Critical section for API URL change cache issues
- **Added**: Step-by-step resolution guide

## Deployment Steps

### For This Specific Issue:
```bash
# 1. Force cache refresh (already done)
npm run force-cache-refresh

# 2. Build with fresh cache (already done)
npm run build

# 3. Deploy to Vercel
git add .
git commit -m "Fix: Force cache refresh for new API URL"
git push origin main

# 4. Manual Vercel cache clearing
# Go to Vercel Dashboard → Project → Functions → "Purge Cache"
```

### For Future API URL Changes:
```bash
# Quick fix command
npm run force-cache-refresh && npm run build

# Then deploy normally
```

## Verification Steps

### After Deployment:
1. **Check Network Tab**: Verify requests go to `https://api.umugoreuzashimwa.org`
2. **Test API Endpoints**: Ensure data loads correctly
3. **Check Service Worker**: Verify new cache version is active
4. **Test Offline/Online**: Ensure caching still works properly

### User-Side Cache Clearing:
- **Hard Refresh**: Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)
- **Clear Browser Data**: Clear site data in browser settings
- **Service Worker**: Will auto-update on next visit

## Prevention for Future

### Best Practices:
1. **Always run cache refresh script** when changing API URLs
2. **Update cache versions** in service worker for major changes
3. **Use Vercel cache purging** after deployments with API changes
4. **Test in incognito mode** to verify cache clearing worked

### Monitoring:
- Check browser Network tab for correct API URLs
- Monitor service worker updates in DevTools
- Verify cache-bust manifest timestamps

## Files Modified
- `public/sw.js` - Service worker cache strategy
- `src/lib/api.js` - API cache busting
- `public/cache-bust.json` - Cache manifest
- `scripts/force-cache-refresh.js` - New cache management script
- `package.json` - New npm scripts
- `README.md` - Documentation updates

## Technical Details

### Cache Strategy Changes:
- **Before**: Cache-first strategy (return cached response, update in background)
- **After**: Network-first strategy for API requests (fetch fresh, fallback to cache)

### Version Management:
- Service worker cache version: Timestamp-based
- API cache buster: Version parameter in URLs
- Build cache: Cleared on each fresh build

This comprehensive solution ensures that API URL changes are immediately reflected in production deployments and prevents similar caching issues in the future.
